<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, onMounted, onUnmounted, computed } from 'vue'
import {
  VideoPlay,
  Star,
  TrendCharts,
  DataAnalysis,
  Connection,

  User,
  Document,
  Goods,
  Opportunity,
  Tools,
  Operation,
  Medal,
  CopyDocument,
  ArrowLeft,
  ArrowRight
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()
const isPageLoaded = ref(false)
const showContent = ref(false)

const goBack = () => {
  router.push('/card/WanWangKeJi')
}

const goToAIPromoter = () => {
  router.push('/card/WanWangKeJi/ai-promoter')
}

// 添加访问网站的方法
const visitWebsite = () => {
  window.open('https://www.jxxz.gov.cn/', '_blank')
}

// 数字滚动动画
const animatedNumbers = ref({
  years: 0,
  clients: 0,
  projects: 0,
  copyrights: 0
})

// 荣誉证书数据
const honorCertificates = ref([
  {
    title: '高新技术企业',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/GaoXinJiShu.jpg'
  },
  {
    title: '捐赠证书',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/JuanZengZhengShu.png'
  },
  {
    title: '杰出贡献奖',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/JieChuGongXian.jpg'
  },
  {
    title: '副会长单位',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/FuHuiZhangDanWei.jpg'
  },
  {
    title: '爱心企业',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/AiXinQiYe.jpg'
  },
  {
    title: '荣誉证书',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/RongYuZhengShu.png'
  }
])

// 专利证书数据
const patentCertificates = ref([
  {
    title: '万网公安合成研判管理软件',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/GongAnHeChengYanPanGuanLi.png'
  },
  {
    title: '万网科技政务随机编号软件',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/KeJiZhengWuSuiJiBianHao.png'
  },
  {
    title: '万网城管执法通管理软件',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/ChenGuanZhiFaTong.png'
  },
  {
    title: '万网智慧党建软件',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/ZhiHuiDangJian.png'
  },
  {
    title: '万网互联网医院信息系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/HuLianWangYiYuanGuanLi.jpg'
  },
  {
    title: '万网医院信息管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/YiYuanXinXiGuanLi.jpg'
  },
  {
    title: '上饶市万网政企招采平台',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/ZhengQiZhaoCai.png'
  },
  {
    title: '义警大数据管理平台',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/YiJingDaShuJuGuanLiPingTai.jpg'
  },
  {
    title: '公安流动人口管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/LiuDongRenKouGuanLiXiTong.jpg'
  },
  {
    title: '智凡政务管理软件',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/ZhengShiZhengWuGuanLi.png'
  },
  {
    title: '疫苗接种预约管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/YuYueGuanLi.jpg'
  },
  {
    title: '舆情督办监测管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ZhengShu/YuQingDuBanJianCe.jpg'
  }
])

// 证书轮播状态
const honorCurrentIndex = ref(0)
const patentCurrentIndex = ref(0)

// 响应式检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 767
}

// 证书轮播控制函数
const nextHonorSlide = () => {
  honorCurrentIndex.value = (honorCurrentIndex.value + 1) % honorCertificates.value.length
}

const prevHonorSlide = () => {
  honorCurrentIndex.value = honorCurrentIndex.value === 0
    ? honorCertificates.value.length - 1
    : honorCurrentIndex.value - 1
}

const nextPatentSlide = () => {
  patentCurrentIndex.value = (patentCurrentIndex.value + 1) % patentCertificates.value.length
}

const prevPatentSlide = () => {
  patentCurrentIndex.value = patentCurrentIndex.value === 0
    ? patentCertificates.value.length - 1
    : patentCurrentIndex.value - 1
}

// 获取当前显示的证书
const getCurrentHonorCertificates = computed(() => {
  const itemsPerSlide = isMobile.value ? 1 : 3
  const start = honorCurrentIndex.value * itemsPerSlide
  return honorCertificates.value.slice(start, start + itemsPerSlide)
})

const getCurrentPatentCertificates = computed(() => {
  const itemsPerSlide = isMobile.value ? 1 : 3
  const start = patentCurrentIndex.value * itemsPerSlide
  return patentCertificates.value.slice(start, start + itemsPerSlide)
})

// 自动轮播
let honorTimer: number | null = null
let patentTimer: number | null = null

const startAutoPlay = () => {
  // 荣誉证书自动轮播
  honorTimer = setInterval(() => {
    nextHonorSlide()
  }, 4000)

  // 专利证书自动轮播
  patentTimer = setInterval(() => {
    nextPatentSlide()
  }, 5000)
}

const stopAutoPlay = () => {
  if (honorTimer) {
    clearInterval(honorTimer)
    honorTimer = null
  }
  if (patentTimer) {
    clearInterval(patentTimer)
    patentTimer = null
  }
}

// 图片错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  console.warn('证书图片加载失败:', img.src)
}

const targetNumbers = {
  years: 10,
  clients: 500,
  projects: 3000,
  copyrights: 50
}

const animateNumbers = () => {
  const duration = 2000 // 2秒
  const steps = 60
  const stepTime = duration / steps

  Object.keys(targetNumbers).forEach(key => {
    const target = targetNumbers[key as keyof typeof targetNumbers]
    const increment = target / steps
    let current = 0

    const timer = setInterval(() => {
      current += increment
      if (current >= target) {
        (animatedNumbers.value as any)[key] = target
        clearInterval(timer)
      } else {
        (animatedNumbers.value as any)[key] = Math.floor(current)
      }
    }, stepTime)
  })
}



// 页面加载动画
const initPageAnimation = () => {
  // 延迟显示内容，创建加载效果
  setTimeout(() => {
    isPageLoaded.value = true
    setTimeout(() => {
      showContent.value = true
    }, 300)
  }, 800)
}

onMounted(() => {
  // 初始化页面动画
  initPageAnimation()

  setTimeout(() => {
    animateNumbers()
  }, 1200)

  setTimeout(() => {
    startAutoPlay()
  }, 1500)

  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  stopAutoPlay()
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <!-- 页面加载动画 -->
    <div v-if="!isPageLoaded" class="page-loader">
      <div class="loader-content">
        <div class="loader-logo">
          <div class="logo-circle">
            <div class="logo-inner"></div>
          </div>
        </div>
        <div class="loader-text">万网科技</div>
        <div class="loader-progress">
          <div class="progress-bar"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-show="isPageLoaded" class="main-wrapper" :class="{ 'content-visible': showContent }">
      <div class="header">
        <button @click="goBack" class="back-btn">
          ‹ 返回
        </button>
        <h1>企业介绍</h1>
      </div>

    <div class="content">
      <!-- 首页介绍 -->
      <div class="section">
        <div class="hero-card">
          <div class="hero-content">
            <h1 class="company-title">江西万网科技</h1>
            <div class="subtitle-container">
              <p class="subtitle">数字化转型的实践者与赋能者</p>
              <p class="subtitle highlight">国家高新技术企业</p>
            </div>
            <p class="slogan">技术驱动价值，数据赋能未来。</p>

            <!-- 关键数据 -->
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ animatedNumbers.years }}+</div>
                <div class="stat-label">年行业深耕</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ animatedNumbers.clients }}+</div>
                <div class="stat-label">家信赖客户</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ animatedNumbers.projects }}+</div>
                <div class="stat-label">个成功项目</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ animatedNumbers.copyrights }}+</div>
                <div class="stat-label">项软件著作权</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 宣传视频 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><VideoPlay /></el-icon>
            企业宣传视频
          </h2>
          <div class="video-container">
            <video controls style="width: 100%; border-radius: 8px;">
              <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/WanWangKeJi/xcsp.mp4" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </div>
        </div>
      </div>

      <!-- 关于我们 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Star /></el-icon>
            关于我们 · 不止是技术，更是懂业务的合作伙伴
          </h2>
          <div class="about-content">
            <p class="summary">
              江西万网科技是一家专业从事软件研发、大数据分析、系统集成等信息技术服务的国家高新技术企业。公司成立于2012年，公司积极倡导创新价值，紧跟互联网发展趋势，现如今发展为集政府门户网站建设、小程序开发、APP应用开发、智慧应用系统开发、大数据应用产品开发、AI应用技术产品开发等为一体的创新驱动型互联网软件开发公司。
            </p>
            <div class="vision-mission">
              <div class="vision">
                <h4>我们的愿景</h4>
                <p>成为区域内领先的"互联网+"服务及行业应用解决方案服务商。</p>
              </div>
              <div class="values">
                <h4>我们的价值观</h4>
                <p>诚信、创新、共享、共赢。我们坚信，客户的成功才是我们最大的价值。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心能力 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><TrendCharts /></el-icon>
            我们如何为您创造价值
          </h2>
          <div class="capabilities-grid">
            <div class="capability-item">
              <div class="capability-icon">
                <el-icon><Star /></el-icon>
              </div>
              <h3>定制化软件研发</h3>
              <p>我们为您量身打造专属的业务系统、管理平台和APP。从一个想法到一套成熟的软件，我们帮您实现，让软件完美适配您的业务，而不是让业务去将就软件。</p>
            </div>
            <div class="capability-item">
              <div class="capability-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <h3>大数据分析与应用</h3>
              <p>通过建立数据大屏和智能报表，将杂乱的数据转化为直观的图表和深刻的洞察，助您看清业务全貌，发现潜在风险与机遇，做出更科学的决策。</p>
            </div>
            <div class="capability-item">
              <div class="capability-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <h3>复杂系统集成</h3>
              <p>我们就像"系统连接器"，能将您现有的各个新老系统、软件硬件无缝对接，打通数据壁垒，实现信息自动流转，极大提升工作效率。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心竞争力 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Star /></el-icon>
            核心竞争力
          </h2>
          <div class="competitive-advantages-grid">
            <!-- 核心竞争力一：丰富的行业经验 -->
            <div class="advantage-card">
              <div class="advantage-icon">
                <el-icon><Document /></el-icon>
              </div>
              <h3>十年沉淀·深耕政企</h3>
              <p>十年经验，更懂您的业务。我们深刻理解政务、公安、国企等行业的特殊需求与业务逻辑，提供真正贴合实际、解决核心问题的行业解决方案。</p>
            </div>

            <!-- 核心竞争力二：产品化定制开发 -->
            <div class="advantage-card">
              <div class="advantage-icon">
                <el-icon><Goods /></el-icon>
              </div>
              <h3>全栈自研 · 技术为核</h3>
              <p>坚持核心技术自主研发，拒绝外包与模板。我们拥有完整的全栈技术团队，确保项目的高质量、高可控性与长期技术保障。</p>
            </div>

            <!-- 核心竞争力三：专业化创新团队 -->
            <div class="advantage-card">
              <div class="advantage-icon">
                <el-icon><Opportunity /></el-icon>
              </div>
              <h3>产品思维 · 创造价值</h3>
              <p>我们交付的不是一个项目，而是一个有生命力的产品。我们以产品经理的视角贯穿始终，注重用户体验与持续价值，让软件不仅"能用"，更"好用"。</p>
            </div>

            <!-- 核心竞争力四：标准化的服务保障 -->
            <div class="advantage-card">
              <div class="advantage-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <h3>规范体系 · 服务保障</h3>
              <p>我们用成熟的体系保障每一次成功合作。标准化的项目流程与快速响应的售后支持，确保项目在预算内按时、高质量交付，让您全程无忧。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务流程 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Operation /></el-icon>
            服务流程
          </h2>
          <div class="service-process">
            <div class="process-item">
              <h3>服务流程</h3>
              <div class="process-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/FuWuLiuCheng.png" alt="服务流程图" />
              </div>
            </div>
            <div class="process-item">
              <h3>售后流程</h3>
              <div class="process-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/ShouHouLiuCheng.png" alt="售后流程图" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 合作伙伴 -->
      <div class="section">
        <div class="card">
          <h2>他们都选择信赖我们</h2>
          <div class="partners-container">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/LogoQiang.png" alt="合作伙伴" class="partners-image" />
          </div>
        </div>
      </div>

      <!-- 荣誉证书 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Medal /></el-icon>
            荣誉资质
          </h2>
          <div class="certificate-carousel-new" @mouseenter="stopAutoPlay" @mouseleave="startAutoPlay">
            <div class="carousel-wrapper">
              <button class="carousel-nav-btn prev" @click="prevHonorSlide">
                <el-icon><ArrowLeft /></el-icon>
              </button>

              <div class="certificate-viewport">
                <div class="certificate-slider" :style="{ transform: `translateX(-${honorCurrentIndex * 100}%)` }">
                  <div
                    v-for="(cert, index) in honorCertificates"
                    :key="index"
                    class="certificate-slide"
                    :class="{
                      'current': index === honorCurrentIndex
                    }"
                  >
                    <div class="certificate-card">
                      <div class="certificate-image-wrapper">
                        <img
                          :src="cert.image"
                          :alt="cert.title"
                          class="certificate-image-new"
                          loading="lazy"
                          @error="handleImageError"
                        />
                      </div>
                      <div class="certificate-info">
                        <h4 class="certificate-title-new">{{ cert.title }}</h4>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <button class="carousel-nav-btn next" @click="nextHonorSlide">
                <el-icon><ArrowRight /></el-icon>
              </button>
            </div>

            <div class="carousel-pagination">
              <span
                v-for="(_, index) in honorCertificates.length"
                :key="index"
                class="pagination-dot"
                :class="{ active: index === honorCurrentIndex }"
                @click="honorCurrentIndex = index"
              ></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 专利证书 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><CopyDocument /></el-icon>
            软件著作权
          </h2>
          <div class="certificate-carousel-new" @mouseenter="stopAutoPlay" @mouseleave="startAutoPlay">
            <div class="carousel-wrapper">
              <button class="carousel-nav-btn prev" @click="prevPatentSlide">
                <el-icon><ArrowLeft /></el-icon>
              </button>

              <div class="certificate-viewport">
                <div class="certificate-slider" :style="{ transform: `translateX(-${patentCurrentIndex * 100}%)` }">
                  <div
                    v-for="(cert, index) in patentCertificates"
                    :key="index"
                    class="certificate-slide"
                    :class="{
                      'current': index === patentCurrentIndex
                    }"
                  >
                    <div class="certificate-card">
                      <div class="certificate-image-wrapper">
                        <img
                          :src="cert.image"
                          :alt="cert.title"
                          class="certificate-image-new"
                          loading="lazy"
                          @error="handleImageError"
                        />
                      </div>
                      <div class="certificate-info">
                        <h4 class="certificate-title-new">{{ cert.title }}</h4>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <button class="carousel-nav-btn next" @click="nextPatentSlide">
                <el-icon><ArrowRight /></el-icon>
              </button>
            </div>

            <div class="carousel-pagination">
              <span
                v-for="(_, index) in patentCertificates.length"
                :key="index"
                class="pagination-dot"
                :class="{ active: index === patentCurrentIndex }"
                @click="patentCurrentIndex = index"
              ></span>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA区域 -->
      <div class="section">
        <div class="cta-card">
          <h2>准备好开启您的数字化升级之旅了吗？</h2>
          <p>无论是具体的项目需求，还是初步的技术咨询，我们都已准备好为您提供专业的服务。</p>
          <div class="cta-buttons">
            <button @click="goToAIPromoter" class="cta-btn primary">
              <el-icon><User /></el-icon>
              一键咨询
            </button>
            <button @click="visitWebsite" class="cta-btn secondary">
              <el-icon><ArrowRight /></el-icon>
              点击访问网站
            </button>
          </div>
        </div>
      </div>
    </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
/* 页面加载动画 */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeOut 0.5s ease 0.8s forwards;
}

.loader-content {
  text-align: center;
  color: white;
  animation: slideUp 0.8s ease;
}

.loader-logo {
  margin-bottom: 2rem;
  animation: pulse 2s ease-in-out infinite;
}

.logo-circle {
  width: 80px;
  height: 80px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: rotate 2s linear infinite;
}

.logo-circle::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 3px solid transparent;
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.logo-inner {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: innerPulse 1.5s ease-in-out infinite alternate;
}

.loader-text {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  letter-spacing: 2px;
  animation: textGlow 2s ease-in-out infinite alternate;
}

.loader-progress {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  margin: 0 auto;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), white, rgba(255, 255, 255, 0.8));
  border-radius: 2px;
  animation: progressLoad 0.8s ease-out;
  transform: translateX(-100%);
}

/* 主内容包装器 */
.main-wrapper {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-wrapper.content-visible {
  opacity: 1;
  transform: translateY(0);
}

/* 加载动画关键帧 */
@keyframes fadeOut {
  to {
    opacity: 0;
    visibility: hidden;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes innerPulse {
  from { transform: scale(0.8); opacity: 0.7; }
  to { transform: scale(1.2); opacity: 1; }
}

@keyframes textGlow {
  from { text-shadow: 0 0 10px rgba(255, 255, 255, 0.5); }
  to { text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6); }
}

@keyframes progressLoad {
  from { transform: translateX(-100%); }
  to { transform: translateX(100%); }
}

/* 全局响应式基础 */
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(22, 147, 210, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.04) 0%, transparent 50%),
    linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  width: 100%;
  overflow-x: hidden;
  position: relative;
}

.view-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231693d2' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
  z-index: 0;
}

.header {
  background:
    linear-gradient(135deg, rgba(22, 147, 210, 0.95), rgba(59, 130, 246, 0.95));
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
  width: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
}

.section {
  margin-bottom: 2.5rem;
}

.content-visible .section {
  opacity: 0;
  transform: translateY(50px) scale(0.95);
  animation: sectionFadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.content-visible .section:nth-child(1) { animation-delay: 0.2s; }
.content-visible .section:nth-child(2) { animation-delay: 0.4s; }
.content-visible .section:nth-child(3) { animation-delay: 0.6s; }
.content-visible .section:nth-child(4) { animation-delay: 0.8s; }
.content-visible .section:nth-child(5) { animation-delay: 1.0s; }
.content-visible .section:nth-child(6) { animation-delay: 1.2s; }

@keyframes sectionFadeIn {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card, .hero-card, .cta-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1.25rem;
  padding: 2rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
  width: 100%;
  box-sizing: border-box;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before, .hero-card::before, .cta-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1693d2, #3b82f6, #06b6d4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before, .hero-card:hover::before, .cta-card:hover::before {
  opacity: 1;
}

.card:hover, .hero-card:hover, .cta-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(22, 147, 210, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* 通用图片响应式 */
img {
  max-width: 100%;
  height: auto;
}

.card h2 {
  color: #1693d2;
  margin: 0 0 1.5rem 0;
  font-size: 1.4rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.card h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #1693d2, #3b82f6);
  border-radius: 2px;
}

/* 首页英雄区域 */
.hero-card {
  background:
    linear-gradient(135deg, #1693d2, #3b82f6),
    radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-card::before {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1)) !important;
  opacity: 1 !important;
}

.hero-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.company-title {
  font-size: 2.2rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
  position: relative;
  z-index: 2;
}

.subtitle-container {
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.1rem;
  margin: 0.5rem 0;
  opacity: 0.9;
}

.subtitle.highlight {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  display: inline-block;
  font-weight: 500;
}

.slogan {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 1.5rem 0;
  opacity: 0.95;
}

/* 数据统计 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-item:hover::before {
  left: 100%;
}

.stat-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* 视频容器 */
.video-container {
  margin-top: 1rem;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: auto;
  max-width: 100%;
  border-radius: 8px;
}

/* 关于我们 */
.about-content {
  line-height: 1.6;
}

.summary {
  color: #555;
  margin-bottom: 1rem;
  position: relative;
}



.vision-mission {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f0f0f0;
}

.vision h4, .values h4 {
  color: #1693d2;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.vision p, .values p {
  color: #666;
  margin: 0;
  line-height: 1.6;
}

/* 核心能力 */
.capabilities-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.capability-item {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border-left: 4px solid #1693d2;
  transition: transform 0.2s;
}

.capability-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.1);
}

.capability-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.capability-item h3 {
  color: #333;
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.capability-item p {
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* 核心竞争力 */
.competitive-advantages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.advantage-card {
  background: #ffffff;
  padding: 0;
  border-radius: 1.5rem;
  border: none;
  transition: all 0.4s ease;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
}

.advantage-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(135deg, #1693d2 0%, #3b82f6 50%, #06b6d4 100%);
  opacity: 0.05;
  z-index: 1;
}

.advantage-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 50px rgba(22, 147, 210, 0.25);
}

.advantage-card:hover::before {
  opacity: 0.1;
}

.advantage-card > * {
  position: relative;
  z-index: 2;
}

.advantage-icon {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.8rem;
  margin: 2rem 2rem 1.5rem 2rem;
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.4);
  border: 4px solid rgba(255, 255, 255, 0.9);
}

.advantage-card h3 {
  color: #1e293b;
  margin: 0 2rem 1.5rem 2rem;
  font-size: 1.4rem;
  font-weight: 800;
  letter-spacing: -0.02em;
  line-height: 1.3;
}

.advantage-card p {
  color: #64748b;
  margin: 0 2rem 1.5rem 2rem;
  line-height: 1.8;
  font-size: 1rem;
}

.advantage-card ul {
  color: #64748b;
  margin: 0 2rem 2rem 2rem;
  padding-left: 1.5rem;
  list-style: none;
}

.advantage-card li {
  margin-bottom: 0.8rem;
  line-height: 1.7;
  position: relative;
  padding-left: 1.5rem;
}

.advantage-card li::before {
  content: '✓';
  position: absolute;
  left: 0;
  top: 0;
  color: #1693d2;
  font-weight: bold;
  font-size: 1.1rem;
}

/* 服务流程 */
.service-process {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.process-item {
  text-align: center;
}

.process-item h3 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.process-image {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.process-image img {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 合作伙伴 */
.partners-container {
  text-align: center;
  margin-top: 1rem;
}

.partners-image {
  width: 100%;
  max-width: 800px;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 简化版证书轮播 */
.certificate-carousel-new {
  margin-top: 2rem;
  position: relative;
}

.carousel-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.certificate-viewport {
  flex: 1;
  overflow: hidden;
  padding: 1rem;
}

.certificate-slider {
  display: flex;
  transition: transform 0.5s ease;
}

.certificate-slide {
  flex: 0 0 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.3;
  transform: scale(0.8);
  transition: all 0.6s ease;
}

.certificate-slide.current {
  opacity: 1;
  transform: scale(1);
  z-index: 3;
}

.certificate-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  max-width: 300px;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.certificate-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #1693d2, #3b82f6, #06b6d4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.certificate-slide.current .certificate-card::before {
  opacity: 1;
}

.certificate-slide.current .certificate-card {
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 16px 32px rgba(22, 147, 210, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.certificate-image-wrapper {
  background: white;
  padding: 1rem;
}

.certificate-image-new {
  width: 100%;
  height: 250px;
  object-fit: contain;
  border-radius: 0.25rem;
}

.certificate-info {
  padding: 1rem;
  text-align: center;
  border-top: 1px solid #f1f5f9;
}

.certificate-title-new {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.certificate-subtitle {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
}

.carousel-nav-btn {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  color: #1693d2;
  font-size: 1.1rem;
  font-weight: 600;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.carousel-nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.carousel-nav-btn:hover::before {
  opacity: 0.1;
}

.carousel-nav-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.15) translateY(-2px);
  box-shadow:
    0 16px 32px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(22, 147, 210, 0.1);
  color: #1693d2;
  border-color: rgba(22, 147, 210, 0.2);
}

.carousel-pagination {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.pagination-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(203, 213, 225, 0.6);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 2px solid transparent;
}

.pagination-dot::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.pagination-dot.active::before {
  opacity: 0.2;
}

.pagination-dot.active {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  transform: scale(1.4);
  box-shadow: 0 4px 8px rgba(22, 147, 210, 0.3);
}

.pagination-dot:hover {
  transform: scale(1.2);
  background: rgba(22, 147, 210, 0.7);
}

.pagination-dot:hover:not(.active) {
  background: #94a3b8;
}

/* CTA区域 */
.cta-card {
  background: linear-gradient(135deg, #f8fafc, #e0f2fe);
  text-align: center;
  border: 2px solid #1693d2;
}

.cta-card h2 {
  color: #1693d2;
  margin: 0 0 1rem 0;
  font-size: 1.4rem;
}

.cta-card p {
  color: #666;
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
}

.cta-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  min-width: 150px;
  justify-content: center;
}

.cta-btn.primary {
  background: #1693d2;
  color: white;
}

.cta-btn.primary:hover {
  background: #1472a8;
  transform: translateY(-1px);
}

.cta-btn.secondary {
  background: #e0f2fe;
  color: #1693d2;
  border: 1px solid #1693d2;
}

.cta-btn.secondary:hover {
  background: #d1e9ff;
  transform: translateY(-1px);
}


/* 响应式设计 */
/* 超小屏幕 */
@media (max-width: 480px) {
  .content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .header {
    padding: 0.5rem 0.75rem;
  }

  .company-title {
    font-size: 1.5rem !important;
  }

  .slogan {
    font-size: 0.9rem !important;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .card, .hero-card, .cta-card {
    padding: 1rem;
  }

  .certificate-carousel-new .carousel-wrapper {
    gap: 0.75rem;
    padding: 0 0.5rem;
  }

  .carousel-nav-btn {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .certificate-viewport {
    padding: 0.5rem;
  }

  .certificate-card {
    max-width: 280px;
  }

  .certificate-image-new {
    height: 200px;
  }

  .certificate-info {
    padding: 0.75rem;
  }

  .certificate-title-new {
    font-size: 0.85rem;
  }

  .partners-image {
    max-width: 100%;
  }

  .process-image img {
    max-width: 100%;
  }

  .video-container {
    margin-top: 0.75rem;
  }

  .video-container video {
    border-radius: 6px;
  }
}

/* 小屏幕 */
@media (max-width: 767px) {
  .container {
    padding: 1rem;
  }

  .hero {
    padding: 3rem 0;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .capabilities-grid {
    grid-template-columns: 1fr;
  }

  .competitive-advantages-grid {
    grid-template-columns: 1fr;
  }

  .service-process {
    grid-template-columns: 1fr;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .btn {
    width: 100%;
  }

  /* 移动端证书轮播调整 */
  .certificate-carousel-new .carousel-wrapper {
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .carousel-nav-btn {
    width: 36px;
    height: 36px;
  }

  .certificate-card {
    max-width: 260px;
  }

  .certificate-image-new {
    height: 180px;
  }

  .carousel-pagination {
    margin-top: 1rem;
  }

  .partners-image {
    max-width: 100%;
  }

  .process-image {
    padding: 0.75rem;
  }

  .video-container video {
    border-radius: 6px;
  }

  /* 移动端证书轮播特殊处理 */
  .certificate-slide.center {
    transform: scale(1);
  }
}

/* 平板设备 */
@media (min-width: 768px) and (max-width: 1023px) {
  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  .company-title {
    font-size: 2.2rem;
  }

  .capabilities-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .competitive-advantages-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .service-process {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .vision-mission {
    grid-template-columns: repeat(2, 1fr);
  }

  .cta-buttons {
    flex-direction: row;
    justify-content: center;
    gap: 1rem;
  }
}

/* 桌面设备 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .company-title {
    font-size: 2.5rem;
  }

  .capabilities-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .competitive-advantages-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .service-process {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .vision-mission {
    grid-template-columns: repeat(2, 1fr);
  }

  .cta-buttons {
    flex-direction: row;
    justify-content: center;
  }
}

/* 大屏幕桌面 */
@media (min-width: 1024px) {
  .content {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .capabilities-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .company-title {
    font-size: 3rem;
  }

  .slogan {
    font-size: 1.4rem;
  }

  .stats-grid {
    gap: 2rem;
  }

  .competitive-advantages-grid {
    gap: 2.5rem;
  }

  .service-process {
    gap: 2.5rem;
  }
}

/* 超大屏幕 */
@media (min-width: 1440px) {
  .content {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .company-title {
    font-size: 3.5rem;
  }

  .slogan {
    font-size: 1.6rem;
  }

  .card, .hero-card, .cta-card {
    padding: 2rem;
  }

  .stats-grid {
    gap: 2.5rem;
  }

  .capabilities-grid {
    gap: 2.5rem;
  }

  .competitive-advantages-grid {
    gap: 3rem;
  }

  .service-process {
    gap: 3rem;
  }
}
</style>
