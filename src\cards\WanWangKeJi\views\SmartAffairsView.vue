<template>
  <div class="smart-affairs-page">
    <!-- 导航按钮 -->
    <NavigationButtons />
    
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuizhengwu.jpeg" alt="智慧政务" />
        </div>
        <div class="header-text">
          <h1>智慧政务</h1>
          <p>赋能政府数字化，构建高效服务型政府。</p>
        </div>
      </div>
    </div>

    <!-- 案例展示 -->
    <div class="cases-section">
      <div class="container">
        <div class="cases-grid">
          <div
            v-for="case_ in cases"
            :key="case_.id"
            class="case-card"
            @click="openCaseModal(case_)"
          >
            <div class="case-image">
              <img :src="case_.image" :alt="case_.title" />
            </div>
            <div class="case-content">
              <h3>{{ case_.title }}</h3>
              <p>{{ case_.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 案例详情弹窗 -->
    <div v-if="selectedCase" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ selectedCase.title }}</h2>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="case-image">
            <img :src="selectedCase.image" :alt="selectedCase.title" />
          </div>
          <div class="case-details">
            <div class="detail-section">
              <h3>项目背景</h3>
              <p>{{ selectedCase.background }}</p>
            </div>
            <div class="detail-section">
              <h3>解决方案</h3>
              <p>{{ selectedCase.solution }}</p>
            </div>
            <div class="detail-section">
              <h3>平台价值</h3>
              <p>{{ selectedCase.value }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

interface Case {
  id: number
  title: string
  description: string
  image: string
  background: string
  solution: string
  value: string
}

const selectedCase = ref<Case | null>(null)

const cases = reactive<Case[]>([
  {
    id: 1,
    title: '辅警培训平台',
    description: '专业化辅警培训系统，提升队伍素质',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/anlifengmiantu/zhihuizhengwu1.jpeg',
    background: '传统辅警培训模式存在培训资源分散、培训效果难以评估、培训成本高等问题，急需建立标准化、系统化的培训体系。',
    solution: '构建专业化辅警培训平台，提供在线课程、考试评估、学习记录等功能，实现辅警培训的标准化和数字化管理。',
    value: '该平台为辅警队伍提供了一个高效、便捷的线上教育与培训渠道。它有效解决了传统培训模式的痛点，促进了辅警职业素质和业务能力的持续提升，是加强辅警队伍规范化、专业化建设的重要技术支撑。'
  }
])

const openCaseModal = (case_: Case) => {
  selectedCase.value = case_
}

const closeModal = () => {
  selectedCase.value = null
}
</script>

<style scoped>
.smart-affairs-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 80px;
}

.page-header {
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
  color: white;
  padding: 2rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header-image img {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  object-fit: cover;
}

.header-text h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.header-text p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.cases-section {
  padding: 3rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.case-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.case-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.case-content {
  padding: 1.5rem;
}

.case-content h3 {
  color: #7c3aed;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.case-content p {
  color: #666;
  line-height: 1.6;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  color: #7c3aed;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 1.5rem;
}

.modal-body .case-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h3 {
  color: #7c3aed;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.detail-section p {
  color: #374151;
  line-height: 1.8;
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .header-text h1 {
    font-size: 2rem;
  }
  
  .cases-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}
</style>
