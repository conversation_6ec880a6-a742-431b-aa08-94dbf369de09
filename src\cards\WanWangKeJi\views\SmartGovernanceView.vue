<template>
  <div class="smart-governance-page">
    <!-- 导航按钮 -->
    <NavigationButtons />
    
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuizhili.jpeg" alt="智慧治理" />
        </div>
        <div class="header-text">
          <h1>智慧治理</h1>
          <p>数字驱动社会治理，提升城市管理效能。</p>
        </div>
      </div>
    </div>

    <!-- 案例展示 -->
    <div class="cases-section">
      <div class="container">
        <div class="cases-grid">
          <div
            v-for="case_ in cases"
            :key="case_.id"
            class="case-card"
            @click="openCaseModal(case_)"
          >
            <div class="case-image">
              <img :src="case_.image" :alt="case_.title" />
            </div>
            <div class="case-content">
              <h3>{{ case_.title }}</h3>
              <p>{{ case_.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 案例详情弹窗 -->
    <div v-if="selectedCase" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ selectedCase.title }}</h2>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="case-image">
            <img :src="selectedCase.image" :alt="selectedCase.title" />
          </div>
          <div class="case-details">
            <div class="detail-section">
              <h3>项目背景</h3>
              <p>{{ selectedCase.background }}</p>
            </div>
            <div class="detail-section">
              <h3>解决方案</h3>
              <p>{{ selectedCase.solution }}</p>
            </div>
            <div class="detail-section">
              <h3>平台价值</h3>
              <p>{{ selectedCase.value }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

interface Case {
  id: number
  title: string
  description: string
  image: string
  background: string
  solution: string
  value: string
}

const selectedCase = ref<Case | null>(null)

const cases = reactive<Case[]>([
  {
    id: 1,
    title: '义警管理平台',
    description: '数字化义警队伍管理，提升社会治理效能',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/anlifengmiantu/zhihuizhili1.jpeg',
    background: '传统义警管理模式存在组织松散、管理困难、激励机制不完善等问题，难以充分发挥义警在社会治理中的重要作用。',
    solution: '构建数字化义警管理平台，实现义警招募、培训、考核、激励全流程管理，提升义警队伍的组织化、规范化水平。',
    value: '人性化参与：平台面向全社会开放，无论是公职人员还是普通市民，都能便捷地参与公益事业。\n功能全覆盖：包含27个子系统、120个功能模块，全面覆盖了从协会介绍、活动报名到线上培训、服务时长统计的全过程。\n模式创新：将传统管理升级为"开放式、民主化"的组织模式，有效破解了义警管理难题，为市域社会治理创新提供了实用方案。'
  }
])

const openCaseModal = (case_: Case) => {
  selectedCase.value = case_
}

const closeModal = () => {
  selectedCase.value = null
}
</script>

<style scoped>
.smart-governance-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 80px;
}

.page-header {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  color: white;
  padding: 2rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header-image img {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  object-fit: cover;
}

.header-text h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.header-text p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.cases-section {
  padding: 3rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.case-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.case-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.case-content {
  padding: 1.5rem;
}

.case-content h3 {
  color: #059669;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.case-content p {
  color: #666;
  line-height: 1.6;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  color: #059669;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 1.5rem;
}

.modal-body .case-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h3 {
  color: #059669;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.detail-section p {
  color: #374151;
  line-height: 1.8;
  font-size: 1rem;
  white-space: pre-line;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .header-text h1 {
    font-size: 2rem;
  }
  
  .cases-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}
</style>
