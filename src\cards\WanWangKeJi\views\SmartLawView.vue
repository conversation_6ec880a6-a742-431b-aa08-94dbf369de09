<template>
  <div class="smart-law-page">
    <!-- 导航按钮 -->
    <NavigationButtons />
    
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuifazhi.jpeg" alt="智慧法治" />
        </div>
        <div class="header-text">
          <h1>智慧法治</h1>
          <p>科技赋能政法，助推平安中国建设。</p>
        </div>
      </div>
    </div>

    <!-- 案例展示 -->
    <div class="cases-section">
      <div class="container">
        <div class="cases-grid">
          <div
            v-for="case_ in cases"
            :key="case_.id"
            class="case-card"
            @click="openCaseModal(case_)"
          >
            <div class="case-image">
              <img :src="case_.image" :alt="case_.title" />
            </div>
            <div class="case-content">
              <h3>{{ case_.title }}</h3>
              <p>{{ case_.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 案例详情弹窗 -->
    <div v-if="selectedCase" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ selectedCase.title }}</h2>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="case-image">
            <img :src="selectedCase.image" :alt="selectedCase.title" />
          </div>
          <div class="case-details">
            <div class="detail-section">
              <h3>项目背景</h3>
              <p>{{ selectedCase.background }}</p>
            </div>
            <div class="detail-section">
              <h3>解决方案</h3>
              <p>{{ selectedCase.solution }}</p>
            </div>
            <div class="detail-section">
              <h3>平台价值</h3>
              <p>{{ selectedCase.value }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

interface Case {
  id: number
  title: string
  description: string
  image: string
  background: string
  solution: string
  value: string
}

const selectedCase = ref<Case | null>(null)

const cases = reactive<Case[]>([
  {
    id: 1,
    title: '智慧法治综合管理平台',
    description: '集成化法治管理系统，提升执法效率',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/anlifengmiantu/zhihuifazhi1.jpeg',
    background: '随着法治建设的深入推进，传统的执法管理模式已难以满足现代化治理需求。需要一个集成化、智能化的平台来统筹管理各类法治工作。',
    solution: '构建智慧法治综合管理平台，整合执法资源，优化工作流程，实现执法全过程数字化管理，提升执法效率和规范性。',
    value: '平台实现了执法工作的标准化、规范化和智能化，大幅提升了执法效率，降低了执法风险，为建设法治政府提供了有力支撑。'
  },
  {
    id: 2,
    title: '数字陈列馆',
    description: '数字化展示平台，传承警队文化',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/anlifengmiantu/zhihuifazhi2.jpeg',
    background: '传统的陈列馆展示方式单一，互动性差，难以充分展现警队文化的深厚内涵和时代特色。',
    solution: '打造数字陈列馆，运用VR、AR等现代技术，创建沉浸式体验空间，生动展示警队历史文化和英雄事迹。',
    value: '数字陈列馆是新时代加强警队文化建设的重要载体。它通过引人入胜的互动体验，向内部民警和外部观众生动地展示了刑警精神，起到了极佳的宣传教育和荣誉激励作用。'
  }
])

const openCaseModal = (case_: Case) => {
  selectedCase.value = case_
}

const closeModal = () => {
  selectedCase.value = null
}
</script>

<style scoped>
.smart-law-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 80px;
}

.page-header {
  background: linear-gradient(135deg, #1693d2 0%, #0ea5e9 100%);
  color: white;
  padding: 2rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header-image img {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  object-fit: cover;
}

.header-text h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.header-text p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.cases-section {
  padding: 3rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.case-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.case-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.case-content {
  padding: 1.5rem;
}

.case-content h3 {
  color: #1693d2;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.case-content p {
  color: #666;
  line-height: 1.6;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  color: #1693d2;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 1.5rem;
}

.modal-body .case-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h3 {
  color: #1693d2;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.detail-section p {
  color: #374151;
  line-height: 1.8;
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .header-text h1 {
    font-size: 2rem;
  }
  
  .cases-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}
</style>
