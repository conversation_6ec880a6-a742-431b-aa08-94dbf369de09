<template>
  <div class="smart-life-page">
    <!-- 导航按钮 -->
    <NavigationButtons />
    
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-image">
          <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuishenghuo.jpeg" alt="智慧生活" />
        </div>
        <div class="header-text">
          <h1>智慧生活</h1>
          <p>科技便利民生，开启智慧城市生活。</p>
        </div>
      </div>
    </div>

    <!-- 案例展示 -->
    <div class="cases-section">
      <div class="container">
        <div class="cases-grid">
          <div
            v-for="case_ in cases"
            :key="case_.id"
            class="case-card"
            @click="openCaseModal(case_)"
          >
            <div class="case-image">
              <img :src="case_.image" :alt="case_.title" />
            </div>
            <div class="case-content">
              <h3>{{ case_.title }}</h3>
              <p>{{ case_.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 案例详情弹窗 -->
    <div v-if="selectedCase" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ selectedCase.title }}</h2>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="case-image">
            <img :src="selectedCase.image" :alt="selectedCase.title" />
          </div>
          <div class="case-details">
            <div class="detail-section">
              <h3>项目背景</h3>
              <p>{{ selectedCase.background }}</p>
            </div>
            <div class="detail-section">
              <h3>解决方案</h3>
              <p>{{ selectedCase.solution }}</p>
            </div>
            <div class="detail-section">
              <h3>平台价值</h3>
              <p>{{ selectedCase.value }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

interface Case {
  id: number
  title: string
  description: string
  image: string
  background: string
  solution: string
  value: string
}

const selectedCase = ref<Case | null>(null)

const cases = reactive<Case[]>([
  {
    id: 1,
    title: '智慧社区服务平台',
    description: '一站式社区服务系统，提升居民生活品质',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/anlifengmiantu/zhihuishenghuo1.jpeg',
    background: '传统社区服务模式存在信息不对称、服务效率低、居民参与度不高等问题，难以满足现代城市居民日益增长的美好生活需求。',
    solution: '构建智慧社区服务平台，整合社区服务资源，提供物业服务、生活缴费、社区活动、邻里互助等功能，打造便捷、高效的社区生活服务体系。',
    value: '平台实现了社区服务的数字化、智能化，大幅提升了服务效率和居民满意度，促进了社区治理现代化，为构建和谐宜居的智慧社区提供了有力支撑。'
  },
  {
    id: 2,
    title: '答题小程序',
    description: '互动式知识学习平台，寓教于乐',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/anlifengmiantu/zhihuishenghuo2.jpeg',
    background: '传统知识学习和考核方式单一、枯燥，参与度和效果不佳，难以满足现代人碎片化学习和互动性学习的需求。',
    solution: '开发答题小程序，提供多样化题型和互动式答题模式，支持个人答题、团队竞赛、排行榜等功能，让学习变得有趣而高效。',
    value: '答题小程序以其便捷、灵活、低成本的优势，成为各类组织开展知识竞赛、业务考核、课后练习、招聘培训等活动的理想工具。它将学习和考核变得简单、有趣，极大地拓宽了知识传播的渠道。'
  }
])

const openCaseModal = (case_: Case) => {
  selectedCase.value = case_
}

const closeModal = () => {
  selectedCase.value = null
}
</script>

<style scoped>
.smart-life-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 80px;
}

.page-header {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  color: white;
  padding: 2rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header-image img {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  object-fit: cover;
}

.header-text h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.header-text p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.cases-section {
  padding: 3rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.case-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.case-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.case-content {
  padding: 1.5rem;
}

.case-content h3 {
  color: #f59e0b;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.case-content p {
  color: #666;
  line-height: 1.6;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  color: #f59e0b;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 1.5rem;
}

.modal-body .case-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h3 {
  color: #f59e0b;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.detail-section p {
  color: #374151;
  line-height: 1.8;
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .header-text h1 {
    font-size: 2rem;
  }
  
  .cases-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}
</style>
